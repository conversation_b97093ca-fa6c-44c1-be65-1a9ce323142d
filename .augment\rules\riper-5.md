# 上下文压缩技术

当您需要在新会话中继续之前的工作，但不想重复所有内容时：

1. **关键信息提取**
   - 请求提取关键信息：
     ```
     请提取这次对话中的关键信息，包括：
     1. 核心问题定义
     2. 关键发现和决策
     3. 当前进展状态
     4. 下一步行动计划
     ```

2. **代码状态快照**
   - 请求创建代码状态快照：
     ```
     请创建一个代码状态快照，包括：
     1. 已修改的文件和关键更改
     2. 待解决的问题
     3. 计划的实现步骤
     ```

3. **结构化摘要模板**
   ```
   # 项目状态摘要
   
   ## 问题定义
   [简洁描述核心问题]
   
   ## 当前进展
   [列出已完成的工作]
   
   ## 待解决问题
   [列出未解决的问题]
   
   ## 下一步计划
   [列出接下来的步骤]
   ```


请根据我们的对话，填充以下项目状态摘要模板：

# 项目状态摘要
   
## 问题定义
[简洁描述核心问题]
   
## 当前进展
[列出已完成的工作]
   
## 待解决问题
[列出未解决的问题]
   
## 下一步计划
[列出接下来的步骤]